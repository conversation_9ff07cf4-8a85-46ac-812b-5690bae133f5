{"name": "ecovolt-backend", "version": "1.0.0", "main": "app.js", "engines": {"node": ">=18.0.0"}, "scripts": {"start": "node app.js", "dev": "node app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["charging-station", "electric-vehicle", "api", "nodejs"], "author": "EcoVolt Team", "license": "ISC", "description": "EcoVolt Charging Station Management API - Backend service for managing electric vehicle charging stations", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "pg": "^8.16.0"}}