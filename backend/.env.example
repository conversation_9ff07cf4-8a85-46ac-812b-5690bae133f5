# EcoVolt Backend Environment Variables

# Server Configuration
PORT=3000
NODE_ENV=production

# Database Configuration (PostgreSQL)
# For Render, you'll get this from your PostgreSQL service
DATABASE_URL=postgresql://username:password@hostname:port/database_name

# Alternative database configuration (if not using DATABASE_URL)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ecovolt
DB_USER=your_username
DB_PASSWORD=your_password

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random
JWT_EXPIRES_IN=7d

# CORS Configuration
# Add your frontend domain here for production
FRONTEND_URL=https://your-frontend-domain.vercel.app

# API Rate Limiting (optional)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Level
LOG_LEVEL=info
