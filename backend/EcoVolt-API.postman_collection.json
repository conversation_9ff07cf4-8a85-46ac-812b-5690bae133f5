{"info": {"name": "EcoVolt API", "description": "API collection for EcoVolt Charging Station Management", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "token", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('token', response.data.token);", "}"]}}]}, {"name": "Login User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('token', response.data.token);", "}"]}}]}, {"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/auth/me", "host": ["{{baseUrl}}"], "path": ["api", "auth", "me"]}}}]}, {"name": "Charging Stations", "item": [{"name": "Get All Chargers", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/chargers", "host": ["{{baseUrl}}"], "path": ["api", "chargers"]}}}, {"name": "Get Chargers with Filters", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/chargers?status=Active&power_output=50&connector_type=Type 2", "host": ["{{baseUrl}}"], "path": ["api", "chargers"], "query": [{"key": "status", "value": "Active"}, {"key": "power_output", "value": "50"}, {"key": "connector_type", "value": "Type 2"}]}}}, {"name": "Create Charger", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Downtown Charging Station\",\n  \"latitude\": 40.7128,\n  \"longitude\": -74.0060,\n  \"status\": \"Active\",\n  \"power_output\": 150,\n  \"connector_type\": \"Type 2\"\n}"}, "url": {"raw": "{{baseUrl}}/api/chargers", "host": ["{{baseUrl}}"], "path": ["api", "chargers"]}}}, {"name": "Get Charger by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/chargers/1", "host": ["{{baseUrl}}"], "path": ["api", "chargers", "1"]}}}, {"name": "Update Charger", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Downtown Charging Station\",\n  \"latitude\": 40.7128,\n  \"longitude\": -74.0060,\n  \"status\": \"Inactive\",\n  \"power_output\": 200,\n  \"connector_type\": \"CCS\"\n}"}, "url": {"raw": "{{baseUrl}}/api/chargers/1", "host": ["{{baseUrl}}"], "path": ["api", "chargers", "1"]}}}, {"name": "Delete Charger", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/chargers/1", "host": ["{{baseUrl}}"], "path": ["api", "chargers", "1"]}}}]}, {"name": "Health Check", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}}]}