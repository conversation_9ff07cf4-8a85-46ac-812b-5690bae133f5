services:
  - type: web
    name: ecovolt-backend
    env: node
    plan: free
    buildCommand: cd backend && npm install
    startCommand: cd backend && npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      - key: DATABASE_URL
        fromDatabase:
          name: ecovolt-db
          property: connectionString
      - key: JWT_SECRET
        generateValue: true
      - key: JWT_EXPIRES_IN
        value: 7d
      - key: FRONTEND_URL
        value: https://ecovolt.vercel.app

  - type: pserv
    name: ecovolt-db
    env: postgresql
    plan: free
    databaseName: ecovolt
    databaseUser: ecovolt_user
